# Makefile for Recruitment Plan

# 主文档名称
MAIN = recruitment_plan

# LaTeX编译器
LATEX = xelatex

# 编译选项
LATEXFLAGS = -interaction=nonstopmode -halt-on-error

# 默认目标
all: $(MAIN).pdf

# 编译PDF
$(MAIN).pdf: $(MAIN).tex
    $(LATEX) $(LATEXFLAGS) $(MAIN).tex
    $(LATEX) $(LATEXFLAGS) $(MAIN).tex

# 清理临时文件
clean:
    rm -f *.aux *.log *.toc *.out *.fdb_latexmk *.fls *.synctex.gz

# 完全清理（包括PDF）
distclean: clean
    rm -f $(MAIN).pdf

# 查看PDF
view: $(MAIN).pdf
    open $(MAIN).pdf

# 强制重新编译
force:
    rm -f $(MAIN).pdf
    make $(MAIN).pdf

.PHONY: all clean distclean view force