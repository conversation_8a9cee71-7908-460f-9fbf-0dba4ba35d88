This is XeTeX, Version 3.141592653-2.6-0.999997 (TeX Live 2025) (preloaded format=xelatex 2025.4.17)  16 JUL 2025 23:25
entering extended mode
 restricted \write18 enabled.
 %&-line parsing enabled.
**recruitment_plan.tex
(./recruitment_plan.tex
LaTeX2e <2024-11-01> patch level 2
L3 programming layer <2025-01-18>
(/usr/local/texlive/2025/texmf-dist/tex/latex/base/article.cls
Document Class: article 2024/06/29 v1.4n Standard LaTeX document class
(/usr/local/texlive/2025/texmf-dist/tex/latex/base/size12.clo
File: size12.clo 2024/06/29 v1.4n Standard LaTeX file (size option)
)
\c@part=\count192
\c@section=\count193
\c@subsection=\count194
\c@subsubsection=\count195
\c@paragraph=\count196
\c@subparagraph=\count197
\c@figure=\count198
\c@table=\count199
\abovecaptionskip=\skip49
\belowcaptionskip=\skip50
\bibindent=\dimen141
)
(/usr/local/texlive/2025/texmf-dist/tex/latex/ctex/ctex.sty
(/usr/local/texlive/2025/texmf-dist/tex/latex/l3kernel/expl3.sty
Package: expl3 2025-01-18 L3 programming layer (loader) 

(/usr/local/texlive/2025/texmf-dist/tex/latex/l3backend/l3backend-xetex.def
File: l3backend-xetex.def 2024-05-08 L3 backend support: XeTeX
\g__graphics_track_int=\count266
\l__pdf_internal_box=\box52
\g__pdf_backend_annotation_int=\count267
\g__pdf_backend_link_int=\count268
))
Package: ctex 2022/07/14 v2.5.10 Chinese adapter in LaTeX (CTEX)

(/usr/local/texlive/2025/texmf-dist/tex/latex/ctex/ctexhook.sty
Package: ctexhook 2022/07/14 v2.5.10 Document and package hooks (CTEX)
)
(/usr/local/texlive/2025/texmf-dist/tex/latex/ctex/ctexpatch.sty
Package: ctexpatch 2022/07/14 v2.5.10 Patching commands (CTEX)
)
(/usr/local/texlive/2025/texmf-dist/tex/latex/base/fix-cm.sty
Package: fix-cm 2020/11/24 v1.1t fixes to LaTeX

(/usr/local/texlive/2025/texmf-dist/tex/latex/base/ts1enc.def
File: ts1enc.def 2001/06/05 v3.0e (jk/car/fm) Standard LaTeX file
LaTeX Font Info:    Redeclaring font encoding TS1 on input line 47.
))
\l__ctex_tmp_int=\count269
\l__ctex_tmp_box=\box53
\l__ctex_tmp_dim=\dimen142
\g__ctex_section_depth_int=\count270
\g__ctex_font_size_int=\count271

(/usr/local/texlive/2025/texmf-dist/tex/latex/ctex/config/ctexopts.cfg
File: ctexopts.cfg 2022/07/14 v2.5.10 Option configuration file (CTEX)
)
(/usr/local/texlive/2025/texmf-dist/tex/latex/ctex/engine/ctex-engine-xetex.def
File: ctex-engine-xetex.def 2022/07/14 v2.5.10 XeLaTeX adapter (CTEX)
(/usr/local/texlive/2025/texmf-dist/tex/xelatex/xecjk/xeCJK.sty
Package: xeCJK 2022/08/05 v3.9.1 Typesetting CJK scripts with XeLaTeX

(/usr/local/texlive/2025/texmf-dist/tex/latex/l3packages/xtemplate/xtemplate.st
y
Package: xtemplate 2024-08-16 L3 Experimental prototype document functions
)
\l__xeCJK_tmp_int=\count272
\l__xeCJK_tmp_box=\box54
\l__xeCJK_tmp_dim=\dimen143
\l__xeCJK_tmp_skip=\skip51
\g__xeCJK_space_factor_int=\count273
\l__xeCJK_begin_int=\count274
\l__xeCJK_end_int=\count275
\c__xeCJK_CJK_class_int=\XeTeXcharclass1
\c__xeCJK_FullLeft_class_int=\XeTeXcharclass2
\c__xeCJK_FullRight_class_int=\XeTeXcharclass3
\c__xeCJK_HalfLeft_class_int=\XeTeXcharclass4
\c__xeCJK_HalfRight_class_int=\XeTeXcharclass5
\c__xeCJK_NormalSpace_class_int=\XeTeXcharclass6
\c__xeCJK_CM_class_int=\XeTeXcharclass7
\c__xeCJK_HangulJamo_class_int=\XeTeXcharclass8
\l__xeCJK_last_skip=\skip52
\c__xeCJK_none_node=\count276
\g__xeCJK_node_int=\count277
\c__xeCJK_CJK_node_dim=\dimen144
\c__xeCJK_CJK-space_node_dim=\dimen145
\c__xeCJK_default_node_dim=\dimen146
\c__xeCJK_CJK-widow_node_dim=\dimen147
\c__xeCJK_normalspace_node_dim=\dimen148
\c__xeCJK_default-space_node_skip=\skip53
\l__xeCJK_ccglue_skip=\skip54
\l__xeCJK_ecglue_skip=\skip55
\l__xeCJK_punct_kern_skip=\skip56
\l__xeCJK_indent_box=\box55
\l__xeCJK_last_penalty_int=\count278
\l__xeCJK_last_bound_dim=\dimen149
\l__xeCJK_last_kern_dim=\dimen150
\l__xeCJK_widow_penalty_int=\count279

LaTeX template Info: Declaring template type 'xeCJK/punctuation' taking 0
(template)           argument(s) on line 2396.

\l__xeCJK_fixed_punct_width_dim=\dimen151
\l__xeCJK_mixed_punct_width_dim=\dimen152
\l__xeCJK_middle_punct_width_dim=\dimen153
\l__xeCJK_fixed_margin_width_dim=\dimen154
\l__xeCJK_mixed_margin_width_dim=\dimen155
\l__xeCJK_middle_margin_width_dim=\dimen156
\l__xeCJK_bound_punct_width_dim=\dimen157
\l__xeCJK_bound_margin_width_dim=\dimen158
\l__xeCJK_margin_minimum_dim=\dimen159
\l__xeCJK_kerning_total_width_dim=\dimen160
\l__xeCJK_same_align_margin_dim=\dimen161
\l__xeCJK_different_align_margin_dim=\dimen162
\l__xeCJK_kerning_margin_width_dim=\dimen163
\l__xeCJK_kerning_margin_minimum_dim=\dimen164
\l__xeCJK_bound_dim=\dimen165
\l__xeCJK_reverse_bound_dim=\dimen166
\l__xeCJK_margin_dim=\dimen167
\l__xeCJK_minimum_bound_dim=\dimen168
\l__xeCJK_kerning_margin_dim=\dimen169
\g__xeCJK_family_int=\count280
\l__xeCJK_fam_int=\count281
\g__xeCJK_fam_allocation_int=\count282
\l__xeCJK_verb_case_int=\count283
\l__xeCJK_verb_exspace_skip=\skip57
 (/usr/local/texlive/2025/texmf-dist/tex/latex/fontspec/fontspec.sty
(/usr/local/texlive/2025/texmf-dist/tex/latex/l3packages/xparse/xparse.sty
Package: xparse 2024-08-16 L3 Experimental document command parser
)
Package: fontspec 2024/05/11 v2.9e Font selection for XeLaTeX and LuaLaTeX

(/usr/local/texlive/2025/texmf-dist/tex/latex/fontspec/fontspec-xetex.sty
Package: fontspec-xetex 2024/05/11 v2.9e Font selection for XeLaTeX and LuaLaTe
X
\l__fontspec_script_int=\count284
\l__fontspec_language_int=\count285
\l__fontspec_strnum_int=\count286
\l__fontspec_tmp_int=\count287
\l__fontspec_tmpa_int=\count288
\l__fontspec_tmpb_int=\count289
\l__fontspec_tmpc_int=\count290
\l__fontspec_em_int=\count291
\l__fontspec_emdef_int=\count292
\l__fontspec_strong_int=\count293
\l__fontspec_strongdef_int=\count294
\l__fontspec_tmpa_dim=\dimen170
\l__fontspec_tmpb_dim=\dimen171
\l__fontspec_tmpc_dim=\dimen172

(/usr/local/texlive/2025/texmf-dist/tex/latex/base/fontenc.sty
Package: fontenc 2021/04/29 v2.0v Standard LaTeX package
)
(/usr/local/texlive/2025/texmf-dist/tex/latex/fontspec/fontspec.cfg)))
(/usr/local/texlive/2025/texmf-dist/tex/xelatex/xecjk/xeCJK.cfg
File: xeCJK.cfg 2022/08/05 v3.9.1 Configuration file for xeCJK package
))
\ccwd=\dimen173
\l__ctex_ccglue_skip=\skip58
)
\l__ctex_ziju_dim=\dimen174

(/usr/local/texlive/2025/texmf-dist/tex/latex/zhnumber/zhnumber.sty
Package: zhnumber 2022/07/14 v3.0 Typesetting numbers with Chinese glyphs
\l__zhnum_scale_int=\count295
\l__zhnum_tmp_int=\count296

(/usr/local/texlive/2025/texmf-dist/tex/latex/zhnumber/zhnumber-utf8.cfg
File: zhnumber-utf8.cfg 2022/07/14 v3.0 Chinese numerals with UTF8 encoding
))
(/usr/local/texlive/2025/texmf-dist/tex/latex/ctex/scheme/ctex-scheme-chinese.d
ef
File: ctex-scheme-chinese.def 2022/07/14 v2.5.10 Chinese scheme for generic (CT
EX)
 (/usr/local/texlive/2025/texmf-dist/tex/latex/ctex/config/ctex-name-utf8.cfg
File: ctex-name-utf8.cfg 2022/07/14 v2.5.10 Caption with encoding UTF-8 (CTEX)
)) (/usr/local/texlive/2025/texmf-dist/tex/latex/tools/indentfirst.sty
Package: indentfirst 2023/07/02 v1.03 Indent first paragraph (DPC)
)
(/usr/local/texlive/2025/texmf-dist/tex/latex/ctex/fontset/ctex-fontset-mac.def
File: ctex-fontset-mac.def 2022/07/14 v2.5.10 macOS fonts definition (CTEX)

(/usr/local/texlive/2025/texmf-dist/tex/latex/ctex/fontset/ctex-fontset-macold.
def
File: ctex-fontset-macold.def 2022/07/14 v2.5.10 macOS fonts definition for Yos
emite or earlier version (CTEX)

Package fontspec Info: 
(fontspec)             Script 'CJK' not explicitly supported within font
(fontspec)             'STSong'. Check the typeset output, and if it is okay
(fontspec)             then ignore this warning. Otherwise a different font
(fontspec)             should be chosen.


Package fontspec Info: 
(fontspec)             Could not resolve font "STKaiti/B" (it probably doesn't
(fontspec)             exist).


Package fontspec Info: 
(fontspec)             Could not resolve font "STHeiti/I" (it probably doesn't
(fontspec)             exist).


Package fontspec Info: 
(fontspec)             Could not resolve font "STSong/BI" (it probably doesn't
(fontspec)             exist).


Package fontspec Info: 
(fontspec)             Font family 'STSong(0)' created for font 'STSong' with
(fontspec)             options
(fontspec)             [Script={CJK},BoldFont={STHeiti},ItalicFont={STKaiti}].
(fontspec)              
(fontspec)              This font family consists of the following NFSS
(fontspec)             series/shapes:
(fontspec)              
(fontspec)             - 'normal' (m/n) with NFSS spec.:
(fontspec)             <->"STSong/OT:script=hani;language=dflt;"
(fontspec)             - 'bold' (b/n) with NFSS spec.:
(fontspec)             <->"STHeiti/OT:script=hani;language=dflt;"
(fontspec)             - 'italic' (m/it) with NFSS spec.:
(fontspec)             <->"STKaiti/OT:script=hani;language=dflt;"

))) (/usr/local/texlive/2025/texmf-dist/tex/latex/ctex/config/ctex.cfg
File: ctex.cfg 2022/07/14 v2.5.10 Configuration file (CTEX)
)
(/usr/local/texlive/2025/texmf-dist/tex/latex/geometry/geometry.sty
Package: geometry 2020/01/02 v5.9 Page Geometry

(/usr/local/texlive/2025/texmf-dist/tex/latex/graphics/keyval.sty
Package: keyval 2022/05/29 v1.15 key=value parser (DPC)
\KV@toks@=\toks17
)
(/usr/local/texlive/2025/texmf-dist/tex/generic/iftex/ifvtex.sty
Package: ifvtex 2019/10/25 v1.7 ifvtex legacy package. Use iftex instead.

(/usr/local/texlive/2025/texmf-dist/tex/generic/iftex/iftex.sty
Package: iftex 2024/12/12 v1.0g TeX engine tests
))
\Gm@cnth=\count297
\Gm@cntv=\count298
\c@Gm@tempcnt=\count299
\Gm@bindingoffset=\dimen175
\Gm@wd@mp=\dimen176
\Gm@odd@mp=\dimen177
\Gm@even@mp=\dimen178
\Gm@layoutwidth=\dimen179
\Gm@layoutheight=\dimen180
\Gm@layouthoffset=\dimen181
\Gm@layoutvoffset=\dimen182
\Gm@dimlist=\toks18
)
(/usr/local/texlive/2025/texmf-dist/tex/latex/fancyhdr/fancyhdr.sty
Package: fancyhdr 2025/02/07 v5.2 Extensive control of page headers and footers

\f@nch@headwidth=\skip59
\f@nch@offset@elh=\skip60
\f@nch@offset@erh=\skip61
\f@nch@offset@olh=\skip62
\f@nch@offset@orh=\skip63
\f@nch@offset@elf=\skip64
\f@nch@offset@erf=\skip65
\f@nch@offset@olf=\skip66
\f@nch@offset@orf=\skip67
\f@nch@height=\skip68
\f@nch@footalignment=\skip69
\f@nch@widthL=\skip70
\f@nch@widthC=\skip71
\f@nch@widthR=\skip72
\@temptokenb=\toks19
)
(/usr/local/texlive/2025/texmf-dist/tex/latex/titlesec/titlesec.sty
Package: titlesec 2025/01/04 v2.17 Sectioning titles
\ttl@box=\box56
\beforetitleunit=\skip73
\aftertitleunit=\skip74
\ttl@plus=\dimen183
\ttl@minus=\dimen184
\ttl@toksa=\toks20
\titlewidth=\dimen185
\titlewidthlast=\dimen186
\titlewidthfirst=\dimen187
)
(/usr/local/texlive/2025/texmf-dist/tex/latex/enumitem/enumitem.sty
Package: enumitem 2025/02/06 v3.11 Customized lists
\labelindent=\skip75
\enit@outerparindent=\dimen188
\enit@toks=\toks21
\enit@inbox=\box57
\enit@count@id=\count300
\enitdp@description=\count301
)
(/usr/local/texlive/2025/texmf-dist/tex/latex/xcolor/xcolor.sty
Package: xcolor 2024/09/29 v3.02 LaTeX color extensions (UK)

(/usr/local/texlive/2025/texmf-dist/tex/latex/graphics-cfg/color.cfg
File: color.cfg 2016/01/02 v1.6 sample color configuration
)
Package xcolor Info: Driver file: xetex.def on input line 274.

(/usr/local/texlive/2025/texmf-dist/tex/latex/graphics-def/xetex.def
File: xetex.def 2022/09/22 v5.0n Graphics/color driver for xetex
)
(/usr/local/texlive/2025/texmf-dist/tex/latex/graphics/mathcolor.ltx)
Package xcolor Info: Model `cmy' substituted by `cmy0' on input line 1349.
Package xcolor Info: Model `RGB' extended on input line 1365.
Package xcolor Info: Model `HTML' substituted by `rgb' on input line 1367.
Package xcolor Info: Model `Hsb' substituted by `hsb' on input line 1368.
Package xcolor Info: Model `tHsb' substituted by `hsb' on input line 1369.
Package xcolor Info: Model `HSB' substituted by `hsb' on input line 1370.
Package xcolor Info: Model `Gray' substituted by `gray' on input line 1371.
Package xcolor Info: Model `wave' substituted by `hsb' on input line 1372.
)
(/usr/local/texlive/2025/texmf-dist/tex/latex/graphics/graphicx.sty
Package: graphicx 2021/09/16 v1.2d Enhanced LaTeX Graphics (DPC,SPQR)

(/usr/local/texlive/2025/texmf-dist/tex/latex/graphics/graphics.sty
Package: graphics 2024/08/06 v1.4g Standard LaTeX Graphics (DPC,SPQR)

(/usr/local/texlive/2025/texmf-dist/tex/latex/graphics/trig.sty
Package: trig 2023/12/02 v1.11 sin cos tan (DPC)
)
(/usr/local/texlive/2025/texmf-dist/tex/latex/graphics-cfg/graphics.cfg
File: graphics.cfg 2016/06/04 v1.11 sample graphics configuration
)
Package graphics Info: Driver file: xetex.def on input line 106.
)
\Gin@req@height=\dimen189
\Gin@req@width=\dimen190
)
(/usr/local/texlive/2025/texmf-dist/tex/latex/booktabs/booktabs.sty
Package: booktabs 2020/01/12 v1.61803398 Publication quality tables
\heavyrulewidth=\dimen191
\lightrulewidth=\dimen192
\cmidrulewidth=\dimen193
\belowrulesep=\dimen194
\belowbottomsep=\dimen195
\aboverulesep=\dimen196
\abovetopsep=\dimen197
\cmidrulesep=\dimen198
\cmidrulekern=\dimen199
\defaultaddspace=\dimen256
\@cmidla=\count302
\@cmidlb=\count303
\@aboverulesep=\dimen257
\@belowrulesep=\dimen258
\@thisruleclass=\count304
\@lastruleclass=\count305
\@thisrulewidth=\dimen259
)
(/usr/local/texlive/2025/texmf-dist/tex/latex/hyperref/hyperref.sty
Package: hyperref 2024-11-05 v7.01l Hypertext links for LaTeX

(/usr/local/texlive/2025/texmf-dist/tex/latex/kvsetkeys/kvsetkeys.sty
Package: kvsetkeys 2022-10-05 v1.19 Key value parser (HO)
)
(/usr/local/texlive/2025/texmf-dist/tex/generic/kvdefinekeys/kvdefinekeys.sty
Package: kvdefinekeys 2019-12-19 v1.6 Define keys (HO)
)
(/usr/local/texlive/2025/texmf-dist/tex/generic/pdfescape/pdfescape.sty
Package: pdfescape 2019/12/09 v1.15 Implements pdfTeX's escape features (HO)

(/usr/local/texlive/2025/texmf-dist/tex/generic/ltxcmds/ltxcmds.sty
Package: ltxcmds 2023-12-04 v1.26 LaTeX kernel commands for general use (HO)
)
(/usr/local/texlive/2025/texmf-dist/tex/generic/pdftexcmds/pdftexcmds.sty
Package: pdftexcmds 2020-06-27 v0.33 Utility functions of pdfTeX for LuaTeX (HO
)

(/usr/local/texlive/2025/texmf-dist/tex/generic/infwarerr/infwarerr.sty
Package: infwarerr 2019/12/03 v1.5 Providing info/warning/error messages (HO)
)
Package pdftexcmds Info: \pdf@primitive is available.
Package pdftexcmds Info: \pdf@ifprimitive is available.
Package pdftexcmds Info: \pdfdraftmode not found.
))
(/usr/local/texlive/2025/texmf-dist/tex/latex/hycolor/hycolor.sty
Package: hycolor 2020-01-27 v1.10 Color options for hyperref/bookmark (HO)
)
(/usr/local/texlive/2025/texmf-dist/tex/latex/hyperref/nameref.sty
Package: nameref 2023-11-26 v2.56 Cross-referencing by name of section

(/usr/local/texlive/2025/texmf-dist/tex/latex/refcount/refcount.sty
Package: refcount 2019/12/15 v3.6 Data extraction from label references (HO)
)
(/usr/local/texlive/2025/texmf-dist/tex/generic/gettitlestring/gettitlestring.s
ty
Package: gettitlestring 2019/12/15 v1.6 Cleanup title references (HO)
 (/usr/local/texlive/2025/texmf-dist/tex/latex/kvoptions/kvoptions.sty
Package: kvoptions 2022-06-15 v3.15 Key value format for package options (HO)
))
\c@section@level=\count306
)
(/usr/local/texlive/2025/texmf-dist/tex/latex/etoolbox/etoolbox.sty
Package: etoolbox 2025/02/11 v2.5l e-TeX tools for LaTeX (JAW)
\etb@tempcnta=\count307
)
(/usr/local/texlive/2025/texmf-dist/tex/generic/stringenc/stringenc.sty
Package: stringenc 2019/11/29 v1.12 Convert strings between diff. encodings (HO
)
)
\@linkdim=\dimen260
\Hy@linkcounter=\count308
\Hy@pagecounter=\count309

(/usr/local/texlive/2025/texmf-dist/tex/latex/hyperref/pd1enc.def
File: pd1enc.def 2024-11-05 v7.01l Hyperref: PDFDocEncoding definition (HO)
)
(/usr/local/texlive/2025/texmf-dist/tex/generic/intcalc/intcalc.sty
Package: intcalc 2019/12/15 v1.3 Expandable calculations with integers (HO)
)
\Hy@SavedSpaceFactor=\count310

(/usr/local/texlive/2025/texmf-dist/tex/latex/hyperref/puenc.def
File: puenc.def 2024-11-05 v7.01l Hyperref: PDF Unicode definition (HO)
)
Package hyperref Info: Option `unicode' set `true' on input line 4040.
Package hyperref Info: Hyper figures OFF on input line 4157.
Package hyperref Info: Link nesting OFF on input line 4162.
Package hyperref Info: Hyper index ON on input line 4165.
Package hyperref Info: Plain pages OFF on input line 4172.
Package hyperref Info: Backreferencing OFF on input line 4177.
Package hyperref Info: Implicit mode ON; LaTeX internals redefined.
Package hyperref Info: Bookmarks ON on input line 4424.
\c@Hy@tempcnt=\count311

(/usr/local/texlive/2025/texmf-dist/tex/latex/url/url.sty
\Urlmuskip=\muskip17
Package: url 2013/09/16  ver 3.4  Verb mode for urls, etc.
)
LaTeX Info: Redefining \url on input line 4763.
\XeTeXLinkMargin=\dimen261

(/usr/local/texlive/2025/texmf-dist/tex/generic/bitset/bitset.sty
Package: bitset 2019/12/09 v1.3 Handle bit-vector datatype (HO)

(/usr/local/texlive/2025/texmf-dist/tex/generic/bigintcalc/bigintcalc.sty
Package: bigintcalc 2019/12/15 v1.5 Expandable calculations on big integers (HO
)
))
\Fld@menulength=\count312
\Field@Width=\dimen262
\Fld@charsize=\dimen263
Package hyperref Info: Hyper figures OFF on input line 6042.
Package hyperref Info: Link nesting OFF on input line 6047.
Package hyperref Info: Hyper index ON on input line 6050.
Package hyperref Info: backreferencing OFF on input line 6057.
Package hyperref Info: Link coloring OFF on input line 6062.
Package hyperref Info: Link coloring with OCG OFF on input line 6067.
Package hyperref Info: PDF/A mode OFF on input line 6072.

(/usr/local/texlive/2025/texmf-dist/tex/latex/base/atbegshi-ltx.sty
Package: atbegshi-ltx 2021/01/10 v1.0c Emulation of the original atbegshi
package with kernel methods
)
\Hy@abspage=\count313
\c@Item=\count314
\c@Hfootnote=\count315
)
Package hyperref Info: Driver (autodetected): hxetex.

(/usr/local/texlive/2025/texmf-dist/tex/latex/hyperref/hxetex.def
File: hxetex.def 2024-11-05 v7.01l Hyperref driver for XeTeX
\pdfm@box=\box58
\c@Hy@AnnotLevel=\count316
\HyField@AnnotCount=\count317
\Fld@listcount=\count318
\c@bookmark@seq@number=\count319

(/usr/local/texlive/2025/texmf-dist/tex/latex/rerunfilecheck/rerunfilecheck.sty
Package: rerunfilecheck 2022-07-10 v1.10 Rerun checks for auxiliary files (HO)
(/usr/local/texlive/2025/texmf-dist/tex/latex/base/atveryend-ltx.sty
Package: atveryend-ltx 2020/08/19 v1.0a Emulation of the original atveryend pac
kage
with kernel methods
)
(/usr/local/texlive/2025/texmf-dist/tex/generic/uniquecounter/uniquecounter.sty
Package: uniquecounter 2019/12/15 v1.4 Provide unlimited unique counter (HO)
)
Package uniquecounter Info: New unique counter `rerunfilecheck' on input line 2
85.
)
\Hy@SectionHShift=\skip76
)
Package hyperref Info: Option `colorlinks' set `false' on input line 35.
 (./recruitment_plan.aux)
\openout1 = `recruitment_plan.aux'.

LaTeX Font Info:    Checking defaults for OML/cmm/m/it on input line 37.
LaTeX Font Info:    ... okay on input line 37.
LaTeX Font Info:    Checking defaults for OMS/cmsy/m/n on input line 37.
LaTeX Font Info:    ... okay on input line 37.
LaTeX Font Info:    Checking defaults for OT1/cmr/m/n on input line 37.
LaTeX Font Info:    ... okay on input line 37.
LaTeX Font Info:    Checking defaults for T1/cmr/m/n on input line 37.
LaTeX Font Info:    ... okay on input line 37.
LaTeX Font Info:    Checking defaults for TS1/cmr/m/n on input line 37.
LaTeX Font Info:    ... okay on input line 37.
LaTeX Font Info:    Checking defaults for TU/lmr/m/n on input line 37.
LaTeX Font Info:    ... okay on input line 37.
LaTeX Font Info:    Checking defaults for OMX/cmex/m/n on input line 37.
LaTeX Font Info:    ... okay on input line 37.
LaTeX Font Info:    Checking defaults for U/cmr/m/n on input line 37.
LaTeX Font Info:    ... okay on input line 37.
LaTeX Font Info:    Checking defaults for PD1/pdf/m/n on input line 37.
LaTeX Font Info:    ... okay on input line 37.
LaTeX Font Info:    Checking defaults for PU/pdf/m/n on input line 37.
LaTeX Font Info:    ... okay on input line 37.

Package fontspec Info: 
(fontspec)             Adjusting the maths setup (use [no-math] to avoid
(fontspec)             this).

\symlegacymaths=\mathgroup4
LaTeX Font Info:    Overwriting symbol font `legacymaths' in version `bold'
(Font)                  OT1/cmr/m/n --> OT1/cmr/bx/n on input line 37.
LaTeX Font Info:    Redeclaring math accent \acute on input line 37.
LaTeX Font Info:    Redeclaring math accent \grave on input line 37.
LaTeX Font Info:    Redeclaring math accent \ddot on input line 37.
LaTeX Font Info:    Redeclaring math accent \tilde on input line 37.
LaTeX Font Info:    Redeclaring math accent \bar on input line 37.
LaTeX Font Info:    Redeclaring math accent \breve on input line 37.
LaTeX Font Info:    Redeclaring math accent \check on input line 37.
LaTeX Font Info:    Redeclaring math accent \hat on input line 37.
LaTeX Font Info:    Redeclaring math accent \dot on input line 37.
LaTeX Font Info:    Redeclaring math accent \mathring on input line 37.
LaTeX Font Info:    Redeclaring math symbol \colon on input line 37.
LaTeX Font Info:    Redeclaring math symbol \Gamma on input line 37.
LaTeX Font Info:    Redeclaring math symbol \Delta on input line 37.
LaTeX Font Info:    Redeclaring math symbol \Theta on input line 37.
LaTeX Font Info:    Redeclaring math symbol \Lambda on input line 37.
LaTeX Font Info:    Redeclaring math symbol \Xi on input line 37.
LaTeX Font Info:    Redeclaring math symbol \Pi on input line 37.
LaTeX Font Info:    Redeclaring math symbol \Sigma on input line 37.
LaTeX Font Info:    Redeclaring math symbol \Upsilon on input line 37.
LaTeX Font Info:    Redeclaring math symbol \Phi on input line 37.
LaTeX Font Info:    Redeclaring math symbol \Psi on input line 37.
LaTeX Font Info:    Redeclaring math symbol \Omega on input line 37.
LaTeX Font Info:    Redeclaring math symbol \mathdollar on input line 37.
LaTeX Font Info:    Redeclaring symbol font `operators' on input line 37.
LaTeX Font Info:    Encoding `OT1' has changed to `TU' for symbol font
(Font)              `operators' in the math version `normal' on input line 37.
LaTeX Font Info:    Overwriting symbol font `operators' in version `normal'
(Font)                  OT1/cmr/m/n --> TU/lmr/m/n on input line 37.
LaTeX Font Info:    Encoding `OT1' has changed to `TU' for symbol font
(Font)              `operators' in the math version `bold' on input line 37.
LaTeX Font Info:    Overwriting symbol font `operators' in version `bold'
(Font)                  OT1/cmr/bx/n --> TU/lmr/m/n on input line 37.
LaTeX Font Info:    Overwriting symbol font `operators' in version `normal'
(Font)                  TU/lmr/m/n --> TU/lmr/m/n on input line 37.
LaTeX Font Info:    Overwriting math alphabet `\mathit' in version `normal'
(Font)                  OT1/cmr/m/it --> TU/lmr/m/it on input line 37.
LaTeX Font Info:    Overwriting math alphabet `\mathbf' in version `normal'
(Font)                  OT1/cmr/bx/n --> TU/lmr/b/n on input line 37.
LaTeX Font Info:    Overwriting math alphabet `\mathsf' in version `normal'
(Font)                  OT1/cmss/m/n --> TU/lmss/m/n on input line 37.
LaTeX Font Info:    Overwriting math alphabet `\mathtt' in version `normal'
(Font)                  OT1/cmtt/m/n --> TU/lmtt/m/n on input line 37.
LaTeX Font Info:    Overwriting symbol font `operators' in version `bold'
(Font)                  TU/lmr/m/n --> TU/lmr/b/n on input line 37.
LaTeX Font Info:    Overwriting math alphabet `\mathit' in version `bold'
(Font)                  OT1/cmr/bx/it --> TU/lmr/b/it on input line 37.
LaTeX Font Info:    Overwriting math alphabet `\mathsf' in version `bold'
(Font)                  OT1/cmss/bx/n --> TU/lmss/b/n on input line 37.
LaTeX Font Info:    Overwriting math alphabet `\mathtt' in version `bold'
(Font)                  OT1/cmtt/m/n --> TU/lmtt/b/n on input line 37.

*geometry* driver: auto-detecting
*geometry* detected driver: xetex
*geometry* verbose mode - [ preamble ] result:
* driver: xetex
* paper: a4paper
* layout: <same size as paper>
* layoutoffset:(h,v)=(0.0pt,0.0pt)
* modes: 
* h-part:(L,W,R)=(71.13188pt, 455.24411pt, 71.13188pt)
* v-part:(T,H,B)=(85.35826pt, 674.33032pt, 85.35826pt)
* \paperwidth=597.50787pt
* \paperheight=845.04684pt
* \textwidth=455.24411pt
* \textheight=674.33032pt
* \oddsidemargin=-1.1381pt
* \evensidemargin=-1.1381pt
* \topmargin=-23.91173pt
* \headheight=14.5pt
* \headsep=25.0pt
* \topskip=12.0pt
* \footskip=30.0pt
* \marginparwidth=35.0pt
* \marginparsep=10.0pt
* \columnsep=10.0pt
* \skip\footins=10.8pt plus 4.0pt minus 2.0pt
* \hoffset=0.0pt
* \voffset=0.0pt
* \mag=1000
* \@twocolumnfalse
* \@twosidefalse
* \@mparswitchfalse
* \@reversemarginfalse
* (1in=72.27pt=25.4mm, 1cm=28.453pt)

Package hyperref Info: Link coloring OFF on input line 37.
(./recruitment_plan.out) (./recruitment_plan.out)
\@outlinefile=\write3
\openout3 = `recruitment_plan.out'.



[1

] (./recruitment_plan.toc
LaTeX Font Info:    External font `cmex10' loaded for size
(Font)              <12> on input line 2.
LaTeX Font Info:    External font `cmex10' loaded for size
(Font)              <8> on input line 2.
LaTeX Font Info:    External font `cmex10' loaded for size
(Font)              <6> on input line 2.
)
\tf@toc=\write4
\openout4 = `recruitment_plan.toc'.



[1]

[2]

[3]

[4]

[5] (./recruitment_plan.aux)
 ***********
LaTeX2e <2024-11-01> patch level 2
L3 programming layer <2022/07/14>
 ***********
Package rerunfilecheck Info: File `recruitment_plan.out' has not changed.
(rerunfilecheck)             Checksum: 4F88A4607C4818A14640FB5A0B4E5D03;1964.
 ) 
Here is how much of TeX's memory you used:
 15522 strings out of 473832
 338351 string characters out of 5729481
 768652 words of memory out of 5000000
 38385 multiletter control sequences out of 15000+600000
 563791 words of font info for 75 fonts, out of 8000000 for 9000
 1348 hyphenation exceptions out of 8191
 93i,11n,114p,376b,473s stack positions out of 10000i,1000n,20000p,200000b,200000s

Output written on recruitment_plan.pdf (6 pages).
