# 鲸语校园商业计划书

这是一份使用LaTeX排版的专业商业计划书模板，专为"鲸语校园"项目设计。

## 文件结构

- `business_plan.tex` - 主要的LaTeX文档
- `Makefile` - 编译脚本
- `README.md` - 说明文档

## 编译要求

确保您的系统已安装以下软件：

1. **LaTeX发行版**（推荐使用以下之一）：
   - macOS: MacTeX
   - Windows: MiKTeX 或 TeX Live
   - Linux: TeX Live

2. **中文字体支持**：
   - 确保系统已安装中文字体
   - LaTeX发行版包含ctex宏包

## 编译方法

### 方法一：使用Makefile（推荐）

```bash
# 编译PDF
make

# 查看PDF（macOS）
make view

# 清理临时文件
make clean

# 完全清理（包括PDF）
make distclean

# 强制重新编译
make force
```

### 方法二：手动编译

```bash
# 编译两次以生成目录和交叉引用
xelatex business_plan.tex
xelatex business_plan.tex
```

## 文档特性

- **专业排版**：使用LaTeX确保高质量的排版效果
- **中文支持**：完整的中文字体和排版支持
- **结构化内容**：清晰的章节结构和目录
- **美观设计**：
  - 自定义的标题格式和颜色
  - 专业的页眉页脚
  - 精美的封面设计
  - 超链接支持

## 内容结构

当前文档包含以下章节：

1. **执行摘要** - 项目核心价值主张
2. **公司描述** - 项目概况、愿景使命、核心理念
3. **产品与服务** - 产品概览、核心功能、差异化优势

## 扩展建议

您可以继续添加以下章节来完善商业计划书：

- 市场分析
- 竞争分析
- 营销策略
- 运营计划
- 管理团队
- 财务预测
- 风险分析
- 融资需求

## 自定义

- 修改 `business_plan.tex` 文件来添加或编辑内容
- 调整颜色主题：修改文档中的 `blue!70!black` 等颜色定义
- 更改字体大小：修改 `\documentclass[12pt,a4paper]{article}` 中的字体大小
- 调整页面布局：修改 `\geometry{}` 中的页边距设置

## 故障排除

如果编译遇到问题：

1. 确保所有必需的LaTeX包都已安装
2. 检查中文字体是否正确安装
3. 尝试使用 `make clean` 清理临时文件后重新编译
4. 查看编译日志文件（.log）获取详细错误信息
